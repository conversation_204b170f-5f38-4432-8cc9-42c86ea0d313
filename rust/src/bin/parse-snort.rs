use std::collections::{HashMap, HashSet};
use std::fs::{File, create_dir_all, remove_dir_all, remove_file};
use std::io::{<PERSON><PERSON><PERSON><PERSON>, BufReader, Write};
use std::path::Path;
use std::sync::Arc;
use std::thread;

use clap::Parser;
use dashmap::DashMap;
use mysql::{
    OptsBuilder,
    prelude::Queryable,
    Pool,
};
use rayon::iter::{
    IntoParallelRefIterator,
    ParallelIterator,
    // ParallelBridge,  // optional if you use `.par_bridge()`
};
use serde_json;

use eterna::utils_classes::{
    SnortConfig,
    SnortParser,
    MYSQLConfig,
    MYSQLValue,
};

use eterna::utils::{
    create_name_of_database,
    get_no_of_infiles,
    evenly_sized_batches,
    create_path_of_infile,
    create_name_of_index,
};

use eterna::utils_parsers::{
    ConfigType,
    parse_ln,
};

// performance constants for large file processing
const FILE_BUFFER_SIZE: usize = 8 * 1024 * 1024;  // 8MB buffer for optimal I/O performance

#[derive(Parser, Debug)]
#[command(author, version, about)]
struct Args {
    #[arg(long = "source-log")]
    source_log: String,
    // /FOO/BAR/BAZ/2025-06-13--Fri.log

    #[arg(long = "log-date")]
    log_date: String,
    // 2020-01-02

    #[arg(long = "already-accomplished", num_args = 0..)]
    already_accomplished: Vec<String>,
    // [] OR ["Sensor-1", "Sensor-2", ...]

    #[arg(long = "sensor-list-of-names", num_args = 1..)]
    sensor_list_of_names: Vec<String>,
    // ["Sensor-1", "Sensor-2", "Sensor-3", ...]

    #[arg(long = "sensor-list-of-names-and-addresses", num_args = 1..)]
    sensor_list_of_names_and_addresses: Vec<String>,
    // ["Sensor-1", "***********", "Sensor-2", "***********", ...]

    #[arg(long = "sensor-dict-of-addresses-and-names")]
    sensor_dict_of_addresses_and_names: String,
    // "{\"***********\": \"Sensor-1\", \"***********\": \"Sensor-2\", ...}"

    #[arg(long = "force", num_args = 0)]
    #[arg(default_value_t = false)]
    force: bool,
    // true/false
}

fn trim_newlines(line: &mut String) {
    while line.ends_with('\n') || line.ends_with('\r') {
        line.pop();
    }
}

fn main() {
    let args = Args::parse();

    // println!("Source log: {:?}", args.source_log);
    // println!("Log date: {:?}", args.log_date);
    // println!("Already accomplished: {:?}", args.already_accomplished);
    // println!("Sensor list of names: {:?}", args.sensor_list_of_names);
    // println!("Sensor list of names and addresses: {:?}", args.sensor_list_of_names_and_addresses);
    // println!("Sensor dict of addresses and names: {:?}", args.sensor_dict_of_addresses_and_names);

    // string -> dict
    let sensor_dict_of_addresses_and_names: HashMap<String, String> =
        serde_json::from_str(&args.sensor_dict_of_addresses_and_names)
            .expect("Failed to parse sensor_dict_of_addresses_and_names");

    // list -> set for O(1) lookup
    let already_accomplished: HashSet<String> =
        args.already_accomplished.into_iter().collect();

    // create dictionary of instances
    let sensor_names_and_instances: DashMap<String, SnortParser> = DashMap::new();
    for s_n in &args.sensor_list_of_names {
        sensor_names_and_instances.insert(
            s_n.clone(),
            SnortParser::new(
                SnortConfig::SLUG.value_string(),
                args.log_date.to_string(),
                s_n.to_string(),
            ),
        );
    }

    // __PARSING__ start

    // open file with larger buffer for better I/O performance
    let file = File::open(&args.source_log)
        .expect(&format!("Failed to open source log: {}", args.source_log));
    let mut reader = BufReader::with_capacity(FILE_BUFFER_SIZE, file);

    println!("parsing...");

    // process file in chunks to avoid loading entire file into memory
    let pool_chunksize = if let eterna::utils_classes::MYSQLValue::Int(size) = MYSQLConfig::POOL_CHUNKSIZE.value() {
        size
    } else {
        panic!("Error getting pool_chunksize from MYSQLConfig");
    };

    loop {
        let mut chunk = Vec::with_capacity(pool_chunksize);

        // read chunk of lines
        for _ in 0..pool_chunksize {
            let mut line = String::new();
            match reader.read_line(&mut line) {
                Ok(0) => break, // EOF
                Ok(_) => {
                    // remove newline character
                    trim_newlines(&mut line);
                    chunk.push(line);
                }
                Err(e) => panic!("Error reading line: {}", e),
            }
        }

        if chunk.is_empty() {
            // EOF reached
            break;
        }

        // process chunk in parallel, aggregate locally to avoid DashMap contention
        let local_results: HashMap<String, Vec<Vec<String>>> = chunk
            .par_iter()
            .map(|line| {
                let (sensor_name, parsed_ln) = parse_ln(
                    line.trim(),
                    ConfigType::Snort,
                    &args.sensor_list_of_names_and_addresses,
                    &sensor_dict_of_addresses_and_names,
                );

                // check if sensor is already accomplished (O(1) lookup)
                if let Some(ref name) = sensor_name {
                    if already_accomplished.contains(name) {
                        return None;
                    }
                }

                match (sensor_name, parsed_ln) {
                    (Some(name), Some(row)) => Some((name, row)),
                    _ => None,
                }
            })
            .filter_map(|x| x)
            .fold(HashMap::new, |mut acc, (name, row)| {
                acc.entry(name).or_insert_with(Vec::new).push(row);
                acc
            })
            .reduce(HashMap::new, |mut acc1, acc2| {
                for (k, mut v) in acc2 {
                    acc1.entry(k).or_insert_with(Vec::new).append(&mut v);
                }
                acc1
            });

        // collect results into sensor instances
        for (name, rows) in local_results {
            if let Some(mut instance) = sensor_names_and_instances.get_mut(&name) {
                instance.rows.extend(rows);
            }
        }

        // println!("Processed {} lines in {:?}", chunk.len(), start.elapsed());
    }

    // __TODO__ temporary
    println!("\nLines parsed per sensor:");
    for entry in sensor_names_and_instances.iter() {
        println!("  {}: {} lines", entry.key(), entry.value().rows.len());
    }

    // __PARSING__ end

    // __DB_HANDLING__ start

    for entry in sensor_names_and_instances.iter() {
        let sensor_name = entry.key();
        let instance = entry.value();

        let dest_dir          = format!("{}/{}/{}", SnortConfig::get_logs_parsed_dir(), sensor_name, args.log_date);
        let accomplished_file = format!("{}/{}-accomplished.log", dest_dir, args.log_date);
        let log_file          = format!("{}/{}.log", dest_dir, args.log_date);

        let database_name = create_name_of_database(&SnortConfig::SLUG.value_string(), &args.log_date, sensor_name);

        // ################################################

        // remove and/or create dest_dir
        if Path::new(&dest_dir).exists() {
            let mut should_rm_dest_dir = false;

            if args.force {
                should_rm_dest_dir = true;
            } else {
                if Path::new(&accomplished_file).exists() {
                    println!("{} for sensor {} is already parsed. skipping", args.log_date, sensor_name);
                    continue;
                } else {
                    should_rm_dest_dir = true;
                }
            };

            if should_rm_dest_dir {
                println!("removing {}", dest_dir);
                if let Err(e) = remove_dir_all(&dest_dir) {
                    eprintln!("Error removing directory {}: {}", dest_dir, e);
                }
                println!("creating {}", dest_dir);
                if let Err(e) = create_dir_all(&dest_dir) {
                    eprintln!("Error creating directory {}: {}", dest_dir, e);
                }
            }
        } else {
            println!("creating {}", dest_dir);
            if let Err(e) = create_dir_all(&dest_dir) {
                eprintln!("Error creating directory {}: {}", dest_dir, e);
            }
        }

        // ################################################

        // START __inserting_into_dbs__

        let mysql_host = match MYSQLConfig::MYSQL_HOST.value() {
            MYSQLValue::Str(host) => host,
            _ => panic!("Error getting MYSQL_HOST"),
        };
        let mysql_user = match MYSQLConfig::MYSQL_MASTER.value() {
            MYSQLValue::Str(user) => user,
            _ => panic!("Error getting MYSQL_MASTER"),
        };
        let mysql_password = match MYSQLConfig::MYSQL_MASTER_PASSWD.value() {
            MYSQLValue::Str(password) => password,
            _ => panic!("Error getting MYSQL_MASTER_PASSWD"),
        };

        let db_opts = OptsBuilder::new()
            .ip_or_hostname(Some(mysql_host))
            .user(Some(mysql_user))
            .pass(Some(mysql_password));

        // drop/create database
        match Pool::new(db_opts) {
            Ok(pool) => {
                match pool.get_conn() {
                    Ok(mut conn) => {
                        println!("dropping database {}", database_name);
                        if let Err(e) = conn.query_drop(format!("DROP DATABASE IF EXISTS {};", database_name)) {
                            eprintln!("Error dropping database {}: {}", database_name, e);
                        }

                        println!("creating database {}", database_name);
                        if let Err(e) = conn.query_drop(format!("CREATE DATABASE {};", database_name)) {
                            eprintln!("Error creating database {}: {}", database_name, e);
                        }
                    }
                    Err(e) => {
                        eprintln!("Error getting database connection: {}", e);
                    }
                }
            }
            Err(e) => {
                eprintln!("Error creating database pool: {}", e);
            }
        }

        // __DB_HANDLING__ end

        // Convert the Python lines 355-478 functionality
        println!("DEBUG: About to call write_instance_rows_and_insert_to_db for sensor: {}", sensor_name);
        write_instance_rows_and_insert_to_db(&instance, &database_name);
    }










    println!("\nParser Finished Successfully");
}

fn write_instance_rows_and_insert_to_db(instance: &SnortParser, database_name: &str) {
    println!("DEBUG: Starting write_instance_rows_and_insert_to_db for database: {}", database_name);
    println!("DEBUG: Instance has {} rows", instance.rows.len());
    // Function to write into infile (equivalent to Python's write_into_infile)
    fn write_into_infile(
        start_of_chunk: usize,
        end_of_chunk: usize,
        infile_path: &str,
        infile_index: usize,
        no_of_infiles: usize,
        instance_rows: &[Vec<String>],
    ) {
        let log_msg = format!(
            "  writing into {} ({}/{}): {} -> {}",
            infile_path, infile_index, no_of_infiles, start_of_chunk, end_of_chunk
        );
        println!("{}", log_msg);

        let mut row_id = start_of_chunk;

        if let Ok(mut file) = std::fs::File::create(infile_path) {
            let end_index = std::cmp::min(end_of_chunk, instance_rows.len());
            println!("DEBUG: start_of_chunk={}, end_of_chunk={}, instance_rows.len()={}, end_index={}",
                     start_of_chunk, end_of_chunk, instance_rows.len(), end_index);
            println!("DEBUG: Will process {} rows", end_index - start_of_chunk);
            let mut rows_written = 0;
            for instance_row in &instance_rows[start_of_chunk..end_index] {
                rows_written += 1;
                row_id += 1;

                // Get MySQL config values
                let terminated_by = if let MYSQLValue::Str(term) = MYSQLConfig::TERMINATED_BY.value() {
                    term
                } else {
                    panic!("Error getting TERMINATED_BY from MYSQLConfig");
                };

                let enclosed_by = if let MYSQLValue::Str(enc) = MYSQLConfig::ENCLOSED_BY.value() {
                    enc
                } else {
                    panic!("Error getting ENCLOSED_BY from MYSQLConfig");
                };

                // Create row with ID: (row_id,) + instance_row
                let mut row_with_id = vec![row_id.to_string()];
                row_with_id.extend(instance_row.clone());

                // Format row: "1"-*@*-"a"-*@*-"b"-*@*-"c"
                let formatted_row = row_with_id
                    .iter()
                    .map(|cell| format!("{}{}{}", enclosed_by, cell, enclosed_by))
                    .collect::<Vec<String>>()
                    .join(&terminated_by);

                if let Err(e) = writeln!(file, "{}", formatted_row) {
                    eprintln!("Error writing to file {}: {}", infile_path, e);
                }
            }
            println!("DEBUG: Actually wrote {} rows to {}", rows_written, infile_path);
        } else {
            eprintln!("Error creating file: {}", infile_path);
        }
    }

    let no_of_infiles = get_no_of_infiles(instance.rows.len());
    println!("DEBUG: no_of_infiles = {}", no_of_infiles);

    if no_of_infiles > 0 {
        println!("{} rows will be inserted into database", instance.rows.len());

        // Get MySQL connection details
        let mysql_host = if let MYSQLValue::Str(host) = MYSQLConfig::MYSQL_HOST.value() {
            host
        } else {
            panic!("Error getting MYSQL_HOST");
        };
        let mysql_user = if let MYSQLValue::Str(user) = MYSQLConfig::MYSQL_MASTER.value() {
            user
        } else {
            panic!("Error getting MYSQL_MASTER");
        };
        let mysql_password = if let MYSQLValue::Str(password) = MYSQLConfig::MYSQL_MASTER_PASSWD.value() {
            password
        } else {
            panic!("Error getting MYSQL_MASTER_PASSWD");
        };

        let db_opts = OptsBuilder::new()
            .ip_or_hostname(Some(mysql_host))
            .user(Some(mysql_user))
            .pass(Some(mysql_password))
            .db_name(Some(database_name));

        println!("DEBUG: Attempting to create database pool...");
        if let Ok(pool) = Pool::new(db_opts) {
            println!("DEBUG: Database pool created successfully");
            if let Ok(mut conn) = pool.get_conn() {
                println!("DEBUG: Database connection established");
                // Create table
                let table_name = SnortConfig::get_table_name();
                println!("creating table {}", table_name);

                let db_columns = if let MYSQLValue::Str(cols) = SnortConfig::DB_COLUMNS.value() {
                    cols
                } else {
                    panic!("Error getting DB_COLUMNS from SnortConfig");
                };

                let create_table_query = format!("CREATE TABLE {} ({});", table_name, db_columns);
                println!("DEBUG: Executing CREATE TABLE query: {}", create_table_query);
                if let Err(e) = conn.query_drop(create_table_query) {
                    eprintln!("Error creating table {}: {}", table_name, e);
                    return;
                }
                println!("DEBUG: Table created successfully");

                println!("{} infiles will be created", no_of_infiles);

                // Process in batches
                for (batch_index, batch) in evenly_sized_batches(no_of_infiles as isize, None).iter().enumerate() {
                    let batch_index = batch_index + 1;
                    println!("batch {}: writing into {} infiles", batch_index, batch.len());

                    let mut handles = vec![];
                    let mut infile_paths = vec![];

                    // Get infile chunksize
                    let infile_chunksize = if let MYSQLValue::Int(size) = MYSQLConfig::INFILE_CHUNKSIZE.value() {
                        size as usize
                    } else {
                        panic!("Error getting INFILE_CHUNKSIZE from MYSQLConfig");
                    };

                    // STEP 1: create n infiles at the same time (using threads)
                    let instance_rows = Arc::new(instance.rows.clone());

                    for &infile_index in batch {
                        let infile_path = create_path_of_infile(database_name, &table_name, Some(infile_index));
                        let start_of_chunk = infile_chunksize * (infile_index - 1);
                        let end_of_chunk = start_of_chunk + infile_chunksize;

                        infile_paths.push(infile_path.clone());

                        let instance_rows_clone = Arc::clone(&instance_rows);
                        let handle = thread::spawn(move || {
                            write_into_infile(
                                start_of_chunk,
                                end_of_chunk,
                                &infile_path,
                                infile_index,
                                no_of_infiles,
                                &instance_rows_clone,
                            );
                        });
                        handles.push(handle);
                    }

                    // Wait for all threads to complete
                    for handle in handles {
                        if let Err(e) = handle.join() {
                            eprintln!("Thread panicked: {:?}", e);
                        }
                    }

                    // STEP 2: insert the n infiles into database one at a time
                    let log_msg = format!(
                        "batch {}: inserting into {} from {} infiles",
                        batch_index, table_name, infile_paths.len()
                    );
                    println!("{}", log_msg);

                    // Sort infile paths naturally
                    let mut sorted_infile_paths = infile_paths.clone();
                    sorted_infile_paths.sort();

                    for (infile_idx, infile_path) in sorted_infile_paths.iter().enumerate() {
                        let infile_idx = infile_idx + 1;

                        if !Path::new(infile_path).exists() {
                            println!("DEBUG: File {} does not exist, skipping", infile_path);
                            continue;
                        }

                        // Check file size
                        if let Ok(metadata) = std::fs::metadata(infile_path) {
                            println!("DEBUG: File {} exists, size: {} bytes", infile_path, metadata.len());
                        }

                        println!("  inserting from {}", infile_path);

                        // Set MySQL options for performance
                        if let Err(e) = conn.query_drop("SET UNIQUE_CHECKS=0;") {
                            eprintln!("Error setting UNIQUE_CHECKS: {}", e);
                        }
                        if let Err(e) = conn.query_drop("SET FOREIGN_KEY_CHECKS=0;") {
                            eprintln!("Error setting FOREIGN_KEY_CHECKS: {}", e);
                        }

                        // Start transaction
                        if let Err(e) = conn.query_drop("START TRANSACTION;") {
                            eprintln!("Error starting transaction: {}", e);
                        }

                        // Get MySQL config values for LOAD DATA statement
                        let infile_statement = MYSQLConfig::get_infile_statement();
                        let terminated_by = if let MYSQLValue::Str(term) = MYSQLConfig::TERMINATED_BY.value() {
                            term
                        } else {
                            panic!("Error getting TERMINATED_BY from MYSQLConfig");
                        };
                        let enclosed_by = if let MYSQLValue::Str(enc) = MYSQLConfig::ENCLOSED_BY.value() {
                            enc
                        } else {
                            panic!("Error getting ENCLOSED_BY from MYSQLConfig");
                        };
                        let db_keys = if let MYSQLValue::Str(keys) = SnortConfig::DB_KEYS.value() {
                            keys
                        } else {
                            panic!("Error getting DB_KEYS from SnortConfig");
                        };

                        let load_data_query = format!(
                            r#"{} "{}"
                            INTO TABLE {}
                            FIELDS TERMINATED BY "{}"
                            ENCLOSED BY '{}'
                            LINES TERMINATED BY "\n"
                            (ID,{});"#,
                            infile_statement, infile_path, table_name, terminated_by, enclosed_by, db_keys
                        );

                        println!("DEBUG: Executing LOAD DATA query: {}", load_data_query);
                        if let Err(e) = conn.query_drop(load_data_query) {
                            eprintln!("Error loading data from {}: {}", infile_path, e);
                        } else {
                            println!("DEBUG: LOAD DATA query executed successfully for {}", infile_path);
                        }
                    }

                    // Commit transaction after batch
                    println!("  committing...");
                    if let Err(e) = conn.query_drop("COMMIT;") {
                        eprintln!("Error committing transaction: {}", e);
                    }

                    // Remove infiles
                    for infile_path in &infile_paths {
                        println!("  removing {}", infile_path);
                        // if let Err(e) = remove_file(infile_path) {
                        //     eprintln!("Error removing file {}: {}", infile_path, e);
                        // }
                    }
                }

                // Create indexes
                let tablenames_and_keys = if let MYSQLValue::Tuple(tuples) = SnortConfig::TABLENAMES_AND_KEYS_FOR_INDEX.value() {
                    tuples
                } else {
                    panic!("Error getting TABLENAMES_AND_KEYS_FOR_INDEX from SnortConfig");
                };

                for (table_name, key) in tablenames_and_keys {
                    let key_str = match key {
                        eterna::utils_classes::StrAndInt::Str(s) => s,
                        eterna::utils_classes::StrAndInt::Int(i) => i.to_string(),
                    };

                    let index_name = create_name_of_index(&key_str);
                    println!("creating index {}", index_name);

                    let index_prefix_length = if let MYSQLValue::Int(len) = MYSQLConfig::INDEX_PREFIX_LENGTH.value() {
                        len
                    } else {
                        panic!("Error getting INDEX_PREFIX_LENGTH from MYSQLConfig");
                    };

                    let index_type = if let MYSQLValue::Str(typ) = MYSQLConfig::INDEX_TYPE.value() {
                        typ
                    } else {
                        panic!("Error getting INDEX_TYPE from MYSQLConfig");
                    };

                    let create_index_query = format!(
                        "CREATE INDEX {} ON {} ({}({})) USING {};",
                        index_name, table_name, key_str, index_prefix_length, index_type
                    );

                    match conn.query_drop(create_index_query) {
                        Ok(_) => {
                            if let Err(e) = conn.query_drop("COMMIT;") {
                                eprintln!("Error committing index creation: {}", e);
                            }
                        }
                        Err(e) => {
                            eprintln!("Error creating index: {:?}", e);
                        }
                    }
                }
            } else {
                eprintln!("Error getting database connection");
            }
        } else {
            eprintln!("Error creating database pool");
        }
    } else {
        println!("DEBUG: no_of_infiles is 0, skipping database operations");
    }
    println!("DEBUG: Finished write_instance_rows_and_insert_to_db");
}
